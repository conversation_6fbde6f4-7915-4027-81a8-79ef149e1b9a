#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UDP数据接收测试脚本
用于测试从C++程序接收雷达数据
"""

import socket
import struct
import time

class LidarUDPReceiver:
    def __init__(self, server_ip='127.0.0.1', server_port=3008):
        self.server_ip = server_ip
        self.server_port = server_port
        self.socket = None
        
    def start_receiving(self):
        """开始接收UDP数据"""
        try:
            # 创建UDP socket
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.socket.bind((self.server_ip, self.server_port))
            
            print(f"UDP接收器启动，监听 {self.server_ip}:{self.server_port}")
            print("等待雷达数据...")
            
            packet_count = 0
            
            while True:
                try:
                    # 接收数据
                    data, addr = self.socket.recvfrom(65536)  # 最大64KB
                    packet_count += 1
                    
                    print(f"\n=== 数据包 #{packet_count} ===")
                    print(f"来源地址: {addr}")
                    print(f"数据长度: {len(data)} 字节")
                    
                    # 尝试解析UserData结构的基本信息
                    if len(data) >= 24:  # 至少包含基本字段
                        try:
                            # 解析前几个字段 (这是一个简化的解析)
                            # DataType type, int idx, char connectArg1[16], int connectArg2
                            type_val = struct.unpack('I', data[0:4])[0]
                            idx = struct.unpack('i', data[4:8])[0]
                            connectArg1 = data[8:24].decode('utf-8', errors='ignore').rstrip('\x00')
                            connectArg2 = struct.unpack('i', data[24:28])[0] if len(data) >= 28 else 0
                            
                            print(f"数据类型: {type_val}")
                            print(f"索引: {idx}")
                            print(f"连接参数1: {connectArg1}")
                            print(f"连接参数2: {connectArg2}")
                            
                        except Exception as e:
                            print(f"解析数据时出错: {e}")
                    
                    # 显示原始数据的前32字节（十六进制）
                    hex_data = ' '.join([f'{b:02x}' for b in data[:32]])
                    print(f"原始数据(前32字节): {hex_data}")
                    
                    if packet_count % 10 == 0:
                        print(f"\n已接收 {packet_count} 个数据包")
                        
                except socket.timeout:
                    print("接收超时，继续等待...")
                    continue
                except KeyboardInterrupt:
                    print("\n用户中断，停止接收")
                    break
                except Exception as e:
                    print(f"接收数据时出错: {e}")
                    continue
                    
        except Exception as e:
            print(f"启动UDP接收器失败: {e}")
        finally:
            if self.socket:
                self.socket.close()
                print("UDP socket已关闭")

def main():
    receiver = LidarUDPReceiver()
    receiver.start_receiving()

if __name__ == "__main__":
    main()
