# 雷达UDP转发测试指南

## 测试步骤

### 1. 检查端口状态
在命令行中运行以下命令检查端口3008是否被占用：
```cmd
netstat -an | findstr :3008
```
如果没有输出，说明端口可用。

### 2. 启动Python UDP接收器
打开**第一个命令行窗口**，导航到项目目录并运行：
```cmd
cd D:\workfile\lanhai-driver-branch_noweb
python test_udp_receiver.py
```
应该看到输出：
```
UDP接收器启动，监听 127.0.0.1:3008
等待雷达数据...
```

### 3. 启动雷达程序
打开**第二个命令行窗口**，运行雷达程序：
```cmd
cd D:\workfile\lanhai-driver-branch_noweb\build\Debug
demo.exe "D:\workfile\lanhai-driver-branch_noweb\config\LDS-E200-A_232.txt"
```

### 4. 观察输出
- **雷达程序窗口**应该显示：
  - `UDP forward initialized: 127.0.0.1:3008`
  - `UDP Forward Success: sent XXX bytes to 127.0.0.1:3008 (count: X)`
  - 雷达数据帧信息

- **Python接收器窗口**应该显示：
  - 接收到的数据包信息
  - 数据长度和内容

### 5. 网络监控命令
在**第三个命令行窗口**中运行以下命令监控网络活动：
```cmd
# 监控端口3008的连接
netstat -an | findstr :3008

# 监控UDP流量（需要管理员权限）
netsh trace start capture=yes tracefile=udp_trace.etl provider=Microsoft-Windows-TCPIP

# 停止监控
netsh trace stop
```

### 6. 使用Wireshark监控
1. 启动Wireshark（需要管理员权限）
2. 选择本地回环接口（Loopback）
3. 设置过滤器：`udp.port == 3008`
4. 开始捕获
5. 观察UDP数据包

### 7. 串口调试助手设置
1. 打开串口调试助手
2. 设置参数：
   - 端口：COM3
   - 波特率：230400
   - 数据位：8
   - 停止位：1
   - 校验位：无
3. 打开串口观察原始数据

## 故障排除

### 问题1：端口被占用
```cmd
# 查找占用端口的进程
netstat -ano | findstr :3008
# 结束进程（替换PID）
taskkill /PID <PID> /F
```

### 问题2：Python脚本无法启动
```cmd
# 检查Python版本
python --version
# 检查socket模块
python -c "import socket; print('Socket module OK')"
```

### 问题3：雷达连接失败
- 检查COM3端口是否被其他程序占用
- 确认雷达硬件连接正常
- 检查配置文件中的串口设置

### 问题4：没有UDP数据
- 确认雷达程序正常运行
- 检查防火墙设置
- 验证IP地址和端口配置

## 预期结果
正常工作时应该看到：
1. 雷达程序成功连接并输出数据帧
2. Python接收器持续接收UDP数据包
3. Wireshark显示127.0.0.1:3008的UDP流量
4. 串口调试助手显示原始雷达数据
