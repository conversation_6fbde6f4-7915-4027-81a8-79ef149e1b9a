#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的UDP接收测试
"""

import socket
import time

def test_udp_receiver():
    try:
        # 创建UDP socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.bind(('127.0.0.1', 3008))
        sock.settimeout(1.0)  # 1秒超时
        
        print("UDP接收器已启动，监听 127.0.0.1:3008")
        print("等待数据... (按Ctrl+C停止)")
        
        packet_count = 0
        
        while True:
            try:
                data, addr = sock.recvfrom(65536)  # 增加缓冲区到64KB
                packet_count += 1
                print(f"[{packet_count}] 接收到 {len(data)} 字节数据，来自 {addr}")
                
                # 显示前16字节的十六进制数据
                hex_data = ' '.join(f'{b:02x}' for b in data[:16])
                print(f"    数据: {hex_data}")
                
            except socket.timeout:
                print(".", end="", flush=True)
                continue
            except KeyboardInterrupt:
                print("\n停止接收")
                break
                
    except Exception as e:
        print(f"错误: {e}")
    finally:
        sock.close()
        print("UDP socket已关闭")

if __name__ == "__main__":
    test_udp_receiver()
