@echo off
echo ========================================
echo 雷达UDP转发测试脚本
echo ========================================
echo.

echo 1. 检查端口3008是否被占用...
netstat -an | findstr :3008
if %errorlevel% == 0 (
    echo 警告: 端口3008已被占用！
    echo 请先关闭占用端口的程序，或者修改配置使用其他端口
    pause
    exit /b 1
) else (
    echo 端口3008可用
)

echo.
echo 2. 启动Python UDP接收器...
echo 请在新的命令行窗口中运行以下命令：
echo python test_udp_receiver.py
echo.
echo 3. 启动雷达程序...
echo 请在另一个命令行窗口中运行以下命令：
echo cd build\Debug
echo demo.exe "D:\workfile\lanhai-driver-branch_noweb\config\LDS-E200-A_232.txt"
echo.
echo 4. 监控网络流量...
echo 可以使用以下命令监控UDP流量：
echo netstat -an | findstr :3008
echo.
echo 按任意键继续...
pause
