@echo off
echo 编译雷达数据转发程序...

REM 设置编译器路径（请根据您的实际情况修改）
REM set COMPILER_PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.30.30705\bin\Hostx64\x64

REM 使用g++编译（如果安装了MinGW）
echo 使用g++编译...
g++ -std=c++11 -I./sdk -o lidar_demo.exe ^
    example/main.cpp ^
    sdk/standard_interface.cpp ^
    sdk/LidarDataProcess.cpp ^
    sdk/Global.cpp ^
    sdk/uart.c ^
    sdk/service/LidarCheckService.cpp ^
    -lws2_32

if %ERRORLEVEL% EQU 0 (
    echo 编译成功！生成文件: lidar_demo.exe
    echo.
    echo 使用方法:
    echo lidar_demo.exe config/LDS-E200-A_232.txt
    echo.
    echo 注意：运行前请确保：
    echo 1. 雷达设备已连接到COM3端口
    echo 2. 波特率设置为230400
    echo 3. Python接收脚本已启动（test_udp_receiver.py）
) else (
    echo 编译失败！请检查编译器设置和源代码。
)

pause
