# 雷达数据UDP转发功能说明

## 功能概述

本程序实现了从COM3串口接收雷达数据，并通过UDP协议转发到本地Python程序的功能。

## 主要修改

### 1. 配置文件修改
- **文件**: `config/LDS-E200-A_232.txt`
- **修改内容**:
  - 波特率: `921600` → `230400`
  - COM端口: `com1` → `com3`

### 2. main.cpp功能增强
- 添加UDP转发功能
- 目标地址: `127.0.0.1:3008`
- 转发雷达点云数据到Python程序

### 3. 新增文件
- `test_udp_receiver.py`: Python UDP接收测试脚本
- `build.bat`: Windows编译脚本
- `UDP_FORWARD_README.md`: 本说明文档

## 使用步骤

### 1. 编译程序
```bash
# Windows (使用MinGW)
build.bat

# 或手动编译
g++ -std=c++11 -I./sdk -o lidar_demo.exe example/main.cpp sdk/*.cpp sdk/service/*.cpp sdk/uart.c -lws2_32
```

### 2. 启动Python接收程序
```bash
python test_udp_receiver.py
```

### 3. 运行雷达程序
```bash
lidar_demo.exe config/LDS-E200-A_232.txt
```

## 数据流程

```
雷达设备 (COM3, 230400) 
    ↓
C++程序 (main.cpp)
    ↓ UDP转发
Python程序 (127.0.0.1:3008)
```

## 数据格式

转发的数据为`UserData`结构体，包含：
- `type`: 数据类型 (FRAMEDATA/SPANDATA)
- `idx`: 帧/扇区索引
- `connectArg1`: 连接参数1 (COM端口名)
- `connectArg2`: 连接参数2 (波特率)
- `framedata`/`spandata`: 点云数据

## 注意事项

1. **硬件连接**: 确保雷达设备正确连接到COM3端口
2. **波特率匹配**: 雷达设备波特率需设置为230400
3. **防火墙**: 确保本地UDP端口3008未被防火墙阻止
4. **运行顺序**: 建议先启动Python接收程序，再启动C++程序

## 故障排除

### 编译错误
- 检查是否安装了MinGW或Visual Studio
- 确认所有源文件路径正确

### 连接失败
- 检查COM3端口是否被其他程序占用
- 验证雷达设备波特率设置
- 使用设备管理器确认COM端口号

### UDP数据接收失败
- 检查Python程序是否正常启动
- 确认端口3008未被占用
- 查看C++程序控制台输出的错误信息

## 扩展功能

可以根据需要进一步扩展：
- 添加数据过滤和处理
- 支持多个目标地址转发
- 添加数据压缩功能
- 实现双向通信控制
