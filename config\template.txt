#connect
type:udp
connectArg:************
connectArg2:6543
local_port:6668
is_group_listener:0
group_ip:**********
#data
output_360:1
from_zero:0
service_port:8888
is_open_service:1
error_circle:3
error_scale:0.9
#fitter#
filter_open:1
max_range:20
min_range:0.5
max_range_difference:0.1
filter_window:1
#get
uuid:-1
model:-1
version:-1
#set
with_smooth:1
with_deshadow:1
resample_res:-1
alarm_msg:-1
rpm:-1
direction:-1
ats:-1
with_start:-1