﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\workfile\lanhai-driver-branch_noweb\example\main.cpp">
      <Filter>example</Filter>
    </ClCompile>
    <ClCompile Include="D:\workfile\lanhai-driver-branch_noweb\sdk\Global.cpp">
      <Filter>sdk</Filter>
    </ClCompile>
    <ClCompile Include="D:\workfile\lanhai-driver-branch_noweb\sdk\LidarDataProcess.cpp">
      <Filter>sdk</Filter>
    </ClCompile>
    <ClCompile Include="D:\workfile\lanhai-driver-branch_noweb\sdk\service\LidarCheckService.cpp">
      <Filter>sdk\service</Filter>
    </ClCompile>
    <ClCompile Include="D:\workfile\lanhai-driver-branch_noweb\sdk\standard_interface.cpp">
      <Filter>sdk</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\workfile\lanhai-driver-branch_noweb\sdk\Global.h">
      <Filter>sdk</Filter>
    </ClInclude>
    <ClInclude Include="D:\workfile\lanhai-driver-branch_noweb\sdk\LidarDataProcess.h">
      <Filter>sdk</Filter>
    </ClInclude>
    <ClInclude Include="D:\workfile\lanhai-driver-branch_noweb\sdk\data.h">
      <Filter>sdk</Filter>
    </ClInclude>
    <ClInclude Include="D:\workfile\lanhai-driver-branch_noweb\sdk\error.h">
      <Filter>sdk</Filter>
    </ClInclude>
    <ClInclude Include="D:\workfile\lanhai-driver-branch_noweb\sdk\service\LidarCheckService.h">
      <Filter>sdk\service</Filter>
    </ClInclude>
    <ClInclude Include="D:\workfile\lanhai-driver-branch_noweb\sdk\standard_interface.h">
      <Filter>sdk</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\workfile\lanhai-driver-branch_noweb\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="example">
      <UniqueIdentifier>{718B62FF-7FAE-323D-B52B-E83BABDA365E}</UniqueIdentifier>
    </Filter>
    <Filter Include="sdk">
      <UniqueIdentifier>{AEB48DD9-7505-3FF4-BB4B-D4F2C9745B37}</UniqueIdentifier>
    </Filter>
    <Filter Include="sdk\service">
      <UniqueIdentifier>{BB9A507C-FE16-300D-AAC3-12DCA056644A}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
