@echo off
echo ========================================
echo 雷达数据转发程序 - 简单编译脚本
echo ========================================
echo.

REM 检查是否在正确的目录
if not exist "example\main.cpp" (
    echo 错误：请在项目根目录运行此脚本
    echo 当前目录应包含 example\main.cpp 文件
    pause
    exit /b 1
)

echo 步骤1：检查编译环境...

REM 检查Visual Studio
where cl >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ 找到Visual Studio编译器
    goto :compile_vs
)

echo ✗ 未找到Visual Studio编译器
echo.
echo ========================================
echo 请按以下步骤操作：
echo ========================================
echo.
echo 1. 安装Visual Studio Community（免费）
echo    下载地址：https://visualstudio.microsoft.com/zh-hans/vs/community/
echo    安装时选择"使用C++的桌面开发"
echo.
echo 2. 安装完成后，在开始菜单搜索并打开：
echo    "Developer Command Prompt for VS 2022"
echo    （或对应的VS版本）
echo.
echo 3. 在Developer Command Prompt中执行：
echo    cd /d "%CD%"
echo    simple_build.bat
echo.
echo ========================================
pause
exit /b 1

:compile_vs
echo.
echo 步骤2：开始编译...
echo.

REM 编译命令
cl /EHsc /I./sdk /Fe:lidar_demo.exe ^
   example/main.cpp ^
   sdk/standard_interface.cpp ^
   sdk/LidarDataProcess.cpp ^
   sdk/Global.cpp ^
   sdk/service/LidarCheckService.cpp ^
   ws2_32.lib

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo ✓ 编译成功！
    echo ========================================
    echo.
    echo 生成文件：lidar_demo.exe
    echo.
    echo 下一步：
    echo 1. 启动Python接收程序：python test_udp_receiver.py
    echo 2. 运行雷达程序：lidar_demo.exe config/LDS-E200-A_232.txt
    echo.
) else (
    echo.
    echo ========================================
    echo ✗ 编译失败！
    echo ========================================
    echo.
    echo 可能的原因：
    echo 1. 缺少Windows SDK
    echo 2. 源代码有错误
    echo 3. 编译器配置问题
    echo.
    echo 建议：重新安装Visual Studio并确保选择了C++开发工具
    echo.
)

pause
